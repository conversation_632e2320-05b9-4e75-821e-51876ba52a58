import { useState } from 'react'
import Card from './components/Card'

import './App.css'

// function App() {
//   const [count, setCount] = useState(0)
//   const handleClick=(e)=>{
//     setCount(count+1)
//   }
//   const negClick = (e)=>{
//     setCount(count-1);
//   }
//   const rukja=() =>{
//     if(count<=0){
//       window.alert("Rukja bete !!")
//     }
//   }
//   const basKr=()=>{
//     if(count>=100){
//       window.alert("Bas kr dungi !!")
//     }
//   }
//   const reset=()=>{
//     setCount(0)
//   }
//   return (
//     <>
//     <h1 classname =" caret-lime-500 text-7xl">Welcome here</h1>
//     <button onClick={negClick}>Barbbad kar dungi </button>{" "}
//     <button onClick={handleClick}>Abbad kr dungi</button>
//     <p>{(count>=0)?count:rukja()}</p>
//     <button onClick={reset}>Reset</button>
//     {/* progress bar based on count value */}
//     <div>{
//       (count>=0 && count<=100)?<progress color='pink' value={count} max="100"></progress>:basKr()
//     }</div>
      
//     </>
//   )
// }

function App(){
  return(
    <div className=' flex justify-center items-center h-screen w-screen'>
      <Card/>
    </div>
  )
}

export default App
