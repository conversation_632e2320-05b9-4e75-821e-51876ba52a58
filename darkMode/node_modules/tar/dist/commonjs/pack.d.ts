/// <reference types="node" />
/// <reference types="node" />
import { type Stats } from 'fs';
import { WriteEntry, WriteEntrySync, WriteEntryTar } from './write-entry.js';
export declare class PackJob {
    path: string;
    absolute: string;
    entry?: WriteEntry | WriteEntryTar;
    stat?: Stats;
    readdir?: string[];
    pending: boolean;
    ignore: boolean;
    piped: boolean;
    constructor(path: string, absolute: string);
}
import { Minipass } from 'minipass';
import * as zlib from 'minizlib';
import { Yallist } from 'yallist';
import { ReadEntry } from './read-entry.js';
import { WarnEvent, type WarnData, type Warner } from './warn-method.js';
declare const ONSTAT: unique symbol;
declare const ENDED: unique symbol;
declare const QUEUE: unique symbol;
declare const CURRENT: unique symbol;
declare const PROCESS: unique symbol;
declare const PROCESSING: unique symbol;
declare const PROCESSJOB: unique symbol;
declare const JOBS: unique symbol;
declare const JOBDONE: unique symbol;
declare const ADDFSENTRY: unique symbol;
declare const ADDTARENTRY: unique symbol;
declare const STAT: unique symbol;
declare const READDIR: unique symbol;
declare const ONREADDIR: unique symbol;
declare const PIPE: unique symbol;
declare const ENTRY: unique symbol;
declare const ENTRYOPT: unique symbol;
declare const WRITEENTRYCLASS: unique symbol;
declare const WRITE: unique symbol;
declare const ONDRAIN: unique symbol;
import { TarOptions } from './options.js';
export declare class Pack extends Minipass<Buffer, ReadEntry | string, WarnEvent<Buffer>> implements Warner {
    opt: TarOptions;
    cwd: string;
    maxReadSize?: number;
    preservePaths: boolean;
    strict: boolean;
    noPax: boolean;
    prefix: string;
    linkCache: Exclude<TarOptions['linkCache'], undefined>;
    statCache: Exclude<TarOptions['statCache'], undefined>;
    file: string;
    portable: boolean;
    zip?: zlib.BrotliCompress | zlib.Gzip;
    readdirCache: Exclude<TarOptions['readdirCache'], undefined>;
    noDirRecurse: boolean;
    follow: boolean;
    noMtime: boolean;
    mtime?: Date;
    filter: Exclude<TarOptions['filter'], undefined>;
    jobs: number;
    [WRITEENTRYCLASS]: typeof WriteEntry | typeof WriteEntrySync;
    onWriteEntry?: (entry: WriteEntry) => void;
    [QUEUE]: Yallist<PackJob>;
    [JOBS]: number;
    [PROCESSING]: boolean;
    [ENDED]: boolean;
    constructor(opt?: TarOptions);
    [WRITE](chunk: Buffer): boolean;
    add(path: string | ReadEntry): this;
    end(cb?: () => void): this;
    end(path: string | ReadEntry, cb?: () => void): this;
    end(path: string | ReadEntry, encoding?: Minipass.Encoding, cb?: () => void): this;
    write(path: string | ReadEntry): boolean;
    [ADDTARENTRY](p: ReadEntry): void;
    [ADDFSENTRY](p: string): void;
    [STAT](job: PackJob): void;
    [ONSTAT](job: PackJob, stat: Stats): void;
    [READDIR](job: PackJob): void;
    [ONREADDIR](job: PackJob, entries: string[]): void;
    [PROCESS](): void;
    get [CURRENT](): PackJob | undefined;
    [JOBDONE](_job: PackJob): void;
    [PROCESSJOB](job: PackJob): void;
    [ENTRYOPT](job: PackJob): TarOptions;
    [ENTRY](job: PackJob): WriteEntry | undefined;
    [ONDRAIN](): void;
    [PIPE](job: PackJob): void;
    pause(): void;
    warn(code: string, message: string | Error, data?: WarnData): void;
}
export declare class PackSync extends Pack {
    sync: true;
    constructor(opt: TarOptions);
    pause(): void;
    resume(): void;
    [STAT](job: PackJob): void;
    [READDIR](job: PackJob): void;
    [PIPE](job: PackJob): void;
}
export {};
//# sourceMappingURL=pack.d.ts.map