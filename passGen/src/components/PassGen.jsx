import { useState } from "react"


export default function Component() {
  const [showPassword, setShowPassword] = useState(false)
  const [length, setLength] = useState([8])
  const [lowercase, setLowercase] = useState(true)
  const [uppercase, setUppercase] = useState(true)
  const [digits, setDigits] = useState(true)
  const [specialChars, setSpecialChars] = useState(true)

  const generatePassword = () => {
    let charset = ""
    if (lowercase) charset += "abcdefghijklmnopqrstuvwxyz"
    if (uppercase) charset += "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    if (digits) charset += "0123456789"
    if (specialChars) charset += "!@#$%^&*()_+-=[]{}|;:,.<>?"
    
    let password = ""
    for (let i = 0; i < length[0]; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length))
    }
    setWifiPassword(password)
  }

  return (
    <div className="min-h-screen bg-slate-900 p-6 flex items-center justify-center">
      <div className="w-full max-w-md space-y-6">
        <div className="space-y-2">
          <h1 className="text-xl font-semibold text-white">Add New Password</h1>
          <p className="text-sm text-slate-400">
            Enter the necessary information to create a new password and save
          </p>
        </div>

        <div className="space-y-4">

          <div className="space-y-2">
              
            <div className="relative">
              <input
                id="wifi-password"
                type={showPassword ? "text" : "password"}
                placeholder="Enter the WiFi password"
                value={wifiPassword}
                onChange={(e) => setWifiPassword(e.target.value)}
                className="bg-slate-800 border-slate-700 text-white placeholder:text-slate-500 focus:border-slate-600 pr-10"
              />
              <button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-slate-400" />
                ) : (
                  <Eye className="h-4 w-4 text-slate-400" />
                )}
              </button>
            </div>
          </div>

          <button
            onClick={generatePassword}
            className="w-full bg-slate-800 hover:bg-slate-700 text-white border border-slate-700"
            variant="outline"
          >
            <Key className="w-4 h-4 mr-2" />
            Generate Automatically
          </button>

          <div className="space-y-4 pt-2">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-white">Length</Label>
                <span className="text-sm text-white">{length[0]}</span>
              </div>
              <Slider
                value={length}
                onValueChange={setLength}
                max={50}
                min={4}
                step={1}
                className="w-full"
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-white">Lowercase Letters</Label>
                <Switch
                  checked={lowercase}
                  onCheckedChange={setLowercase}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-white">Uppercase Letters</Label>
                <Switch
                  checked={uppercase}
                  onCheckedChange={setUppercase}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-white">Digits</Label>
                <Switch
                  checked={digits}
                  onCheckedChange={setDigits}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-white">Special Characters</Label>
                <Switch
                  checked={specialChars}
                  onCheckedChange={setSpecialChars}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
